<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <OutputType>Library</OutputType>
    <AssemblyName>QuantowerDeltaStrategy</AssemblyName>
    <RootNamespace>QuantowerDeltaStrategy</RootNamespace>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <!-- 程序集信息 -->
    <AssemblyTitle>Quantower Delta Tick Strategy</AssemblyTitle>
    <AssemblyDescription>基于200 tick周期Delta指标的交易策略</AssemblyDescription>
    <AssemblyConfiguration>Release</AssemblyConfiguration>
    <AssemblyCompany>Quantower Strategy Development</AssemblyCompany>
    <AssemblyProduct>Quantower Delta Strategy</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
    <Copyright>Copyright © 2024</Copyright>
  </PropertyGroup>

  <!-- 调试配置 -->
  <PropertyGroup Condition="'$(Configuration)'=='Debug'">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <OutputPath>bin\Debug\net8.0-windows\</OutputPath>
  </PropertyGroup>

  <!-- Quantower调试配置 -->
  <PropertyGroup>
    <StartAction>Program</StartAction>
    <StartProgram>D:\CodingSoftWare\Quantower\TradingPlatform\v1.143.14\bin\Quantower.exe</StartProgram>
    <StartArguments>--debug-port=21000</StartArguments>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="TradingPlatform.BusinessLayer">
      <HintPath>D:\CodingSoftWare\Quantower\TradingPlatform\v1.143.14\bin\TradingPlatform.BusinessLayer.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>

</Project>
