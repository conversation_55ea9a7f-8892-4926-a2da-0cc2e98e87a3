<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8</TargetFramework>
    <LangVersion>latest</LangVersion>
    <OutputPath>$(SolutionDir)out/TradingPlatform/v1.0.0/bin/Vendors/$(AssemblyName)</OutputPath>
    <Platforms>AnyCPU;x64</Platforms>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Configurations>Debug;Release;UnitTests;Screener</Configurations>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)'=='Debug'">
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>  
  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <DebugType>none</DebugType>
    <DebugSymbols>false</DebugSymbols>
  </PropertyGroup>
  
  <ItemGroup>
    <None Remove="hit_btc.svg" />
  </ItemGroup>
  
  <ItemGroup>
    <EmbeddedResource Include="hit_btc.svg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="HitBTC.Net" Version="1.1.0" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\..\BusinessLayer\TradingPlatform.BusinessLayer\TradingPlatform.BusinessLayer.csproj" >
      <Private>False</Private>
      <IncludeAssets>compile</IncludeAssets>
    </ProjectReference>
  </ItemGroup>
  
 

</Project>
