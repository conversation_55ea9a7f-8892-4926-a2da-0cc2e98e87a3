// Copyright QUANTOWER LLC. © 2017-2024. All rights reserved.

namespace Bitfinex.API.Models;

public static class BitfinexOrderType
{
    public const string LIMIT = "LIMIT";
    public const string EXCHANGE_LIMIT = "EXCHANGE LIMIT";
    public const string MARKET = "MARKET";
    public const string EXCHANGE_MARKET = "EXCHANGE MARKET";
    public const string STOP = "STOP";
    public const string EXCHANGE_STOP = "EXCHANGE STOP";
    public const string STOP_LIMIT = "STOP LIMIT";
    public const string EXCHANGE_STOP_LIMIT = "EXCHANGE STOP LIMIT";
    public const string TRAILING_STOP = "TRAILING STOP";
    public const string EXCHANGE_TRAILING_STOP = "EXCHANGE TRAILING STOP";
    public const string FOK = "FOK";
    public const string EXCHANGE_FOK = "EXCHANGE FOK";
    public const string IOC = "MARKET";
    public const string EXCHANGE_IOC = "EXCHANGE IOC";
}