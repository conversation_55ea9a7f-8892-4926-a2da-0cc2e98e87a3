<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)'=='Debug'">
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <DebugType>none</DebugType>
    <DebugSymbols>false</DebugSymbols>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Refit" Version="7.1.2" />
    <PackageReference Include="Refit.Newtonsoft.Json" Version="7.1.2" />
    <PackageReference Include="WebSocket4Net" Version="0.15.2" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Models\BitfinexSection.cs" />
  </ItemGroup>

</Project>
