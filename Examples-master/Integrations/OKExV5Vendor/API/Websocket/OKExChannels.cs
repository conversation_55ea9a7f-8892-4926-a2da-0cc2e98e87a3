// Copyright QUANTOWER LLC. © 2017-2024. All rights reserved.

namespace OKExV5Vendor.API.Websocket;

class OKExChannels
{
    public const string TRADES = "trades";
    public const string MARK_PRICE = "mark-price";
    public const string ORDER_BOOK_400 = "books";
    public const string ORDER_BOOK_400_TBT = "books-l2-tbt";
    public const string BBO_TBT = "bbo-tbt";
    public const string TICKERS = "tickers";
    public const string INDEX_TICKERS = "index-tickers";

    public const string INSTRUMENTS = "instruments";
    public const string FUNDING_RATE = "funding-rate";
    public const string OPEN_INTEREST = "open-interest";

    public const string ORDERS = "orders";
    public const string ALGO_ORDERS = "orders-algo";
    public const string ACCOUNT = "account";
    public const string POSITIONS = "positions";
}