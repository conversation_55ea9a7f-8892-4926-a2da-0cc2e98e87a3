// Copyright QUANTOWER LLC. © 2017-2024. All rights reserved.

using Newtonsoft.Json;
using OKExV5Vendor.API.REST.JsonConverters;
using System;

namespace OKExV5Vendor.API.REST.Models;

internal class OKExOrderBook
{
    [JsonConverter(typeof(JsonOrderBookConverter))]
    [JsonProperty("asks")]
    public OKExOrderBookItem[] Asks { get; set; }

    [J<PERSON><PERSON>onverter(typeof(JsonOrderBookConverter))]
    [JsonProperty("bids")]
    public OKExOrderBookItem[] Bids { get; set; }

    [JsonConverter(typeof(JsonStringToLongOrDefaultConverter))]
    [JsonProperty("ts")]
    internal long? _time;
    public DateTime Time => this._time.HasValue ? DateTimeOffset.FromUnixTimeMilliseconds(this._time.Value).UtcDateTime : default;
}

internal struct OKExOrderBookItem
{
    public double Price { get; set; }
    public double Size { get; set; }
}