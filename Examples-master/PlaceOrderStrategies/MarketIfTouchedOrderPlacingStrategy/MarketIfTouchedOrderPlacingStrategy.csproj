<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <OutputPath>$(SolutionDir)out/TradingPlatform/v1.0.0/bin/Scripts/PlaceOrderStrategies</OutputPath>
    <Platforms>AnyCPU;x64</Platforms>
    <Configurations>Debug;Release;UnitTests;Screener</Configurations>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)'=='Debug'">
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <DebugType>none</DebugType>
    <DebugSymbols>false</DebugSymbols>
  </PropertyGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\TradingPlatform.BusinessLayer\TradingPlatform.BusinessLayer.csproj">
      <Private>False</Private>
      <IncludeAssets>compile</IncludeAssets>
    </ProjectReference>
  </ItemGroup>

</Project>
